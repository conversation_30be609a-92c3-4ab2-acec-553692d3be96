/*
 * Example fixes for ClassDefinition.java to handle SimplePassthroughNode casting issues
 * 
 * This file shows the specific changes needed to make ClassDefinition.java work
 * with SimpleClosureConverter that creates SimplePassthroughNode instances.
 */

// Fix 1: processObjLiteral method - Property name resolution
protected void processObjLiteral(ObjectLiteral obj) {
    _configLiteral = obj;
    
    if (_baseCls != null) {
        _extendName = _baseCls;
        addReference(_baseCls, ReferenceType.ClassExtend, null);
        _classRequires.add(_baseCls);
    }
        
    for (ObjectProperty prop : obj.getElements()) {
        BaseNode nameNode = prop.getLeft();
        
        // FIXED: Use safeResolveName instead of resolveName
        _currentPropertyName = AstUtil.safeResolveName(nameNode);
        if (_currentPropertyName == null) {
            continue; // Skip if we can't resolve the property name
        }

        if ("extend".equals(_currentPropertyName)) {
            // FIXED: Use safeResolveName for extend property
            _extendName = AstUtil.safeResolveName(prop.getRight());
            if (_extendName != null) {
                addReference(_extendName, ReferenceType.ClassExtend, prop.getRight());
                _classRequires.add(_extendName);
            }
        } else if ("override".equals(_currentPropertyName)) {
            _overrideNode = prop.getRight();
            
            // FIXED: Safe casting for override property
            StringLiteral overrideLiteral = AstUtil.safeCast(_overrideNode, StringLiteral.class);
            if (overrideLiteral != null) {
                _overrideName = overrideLiteral.getValue();
            } else {
                // Fallback: try to resolve name directly from passthrough node
                _overrideName = AstUtil.safeResolveName(_overrideNode);
            }
            _isOverride = true;
            
        } else if("privates".equals(_currentPropertyName)) {
            BaseNode right = prop.getRight();
            
            // FIXED: Safe casting for ObjectLiteral
            ObjectLiteral configs = AstUtil.safeCast(right, ObjectLiteral.class);
            if (configs != null) {
                for(ObjectProperty p : configs.getElements()) {
                    addMember(p);
                }
            } else {
                // Alternative approach for passthrough nodes
                Map<String, BaseNode> props = AstUtil.safeGetObjectProperties(right);
                for (Map.Entry<String, BaseNode> entry : props.entrySet()) {
                    // Create synthetic ObjectProperty for processing
                    ObjectProperty syntheticProp = AstUtil.createProperty(entry.getKey(), entry.getValue());
                    addMember(syntheticProp);
                }
            }
            
        } else if(_configNames.contains(_currentPropertyName)) {
            BaseNode right = prop.getRight();
            
            // FIXED: Safe handling of config objects
            ObjectLiteral configs = AstUtil.safeCast(right, ObjectLiteral.class);
            if (configs != null) {
                for(ObjectProperty p : configs.getElements()) {
                    String configName = AstUtil.safeResolveName(p.getLeft());
                    if (configName != null) {
                        _currentPropertyName = configName;
                        processDirectives(p);
                        BaseNode value = p.getRight();
                        ClassConfig config = new ClassConfig(this, _currentPropertyName, value);
                        _configs.put(_currentPropertyName, config);
                        p.setMember(config);
                    }
                }
            } else {
                // Handle passthrough config objects
                Map<String, BaseNode> configProps = AstUtil.safeGetObjectProperties(right);
                for (Map.Entry<String, BaseNode> entry : configProps.entrySet()) {
                    String configName = entry.getKey();
                    BaseNode value = entry.getValue();
                    ClassConfig config = new ClassConfig(this, configName, value);
                    _configs.put(configName, config);
                }
            }
        }
    }
}

// Fix 2: addMember method - Safe property name resolution
private void addMember(ObjectProperty prop) {
    BaseNode right = prop.getRight();
    
    // FIXED: Use safeResolveName
    String name = AstUtil.safeResolveName(prop.getLeft());
    if (name == null) {
        return; // Skip if we can't resolve the member name
    }
    
    _currentPropertyName = name;
    ClassMember element;
    if(right instanceof FunctionNode) {
        element = new ClassMethod(this, name, right);
    } else {
        element = new ClassProperty(this, name, right);
    }
    prop.setMember(element);
    _members.put(name, element);
    processDirectives(prop);
}

// Fix 3: initAliases method - Safe array element casting
private void initAliases(ObjectProperty alias) {
    if (alias != null) {
        BaseNode als = alias.getRight();
        String aliasStr;
        
        if (als instanceof ArrayLiteral) {
            for (BaseNode el : ((ArrayLiteral) als).getElements()) {
                // FIXED: Safe casting for array elements
                StringLiteral stringLiteral = AstUtil.safeCast(el, StringLiteral.class);
                if (stringLiteral != null) {
                    aliasStr = stringLiteral.getValue();
                    addAlias(aliasStr);
                } else {
                    // Fallback: try to resolve name from passthrough node
                    aliasStr = AstUtil.safeResolveName(el);
                    if (aliasStr != null) {
                        addAlias(aliasStr);
                    }
                }
            }
        } else if(als instanceof StringLiteral) {
            aliasStr = ((StringLiteral) als).getValue();
            addAlias(aliasStr);
        } else {
            // FIXED: Try safe resolution for passthrough nodes
            aliasStr = AstUtil.safeResolveName(als);
            if (aliasStr != null) {
                addAlias(aliasStr);
            } else {
                CompilerMessage.UnexpectedNodeType.log(als, "Expected String or Array");
            }
        }
    }
}

// Fix 4: initRequires method - Safe array processing
public void initRequires(ObjectProperty requires) {
    if (requires != null) {
        String reqName;
        BaseNode req = requires.getRight();
        
        if (req instanceof ArrayLiteral) {
            for (BaseNode el : ((ArrayLiteral) req).getElements()) {
                // FIXED: Handle both StringLiteral and passthrough nodes
                if(el instanceof StringLiteral) {
                    reqName = ((StringLiteral) el).getValue();
                } else {
                    // Try safe casting first
                    StringLiteral stringLiteral = AstUtil.safeCast(el, StringLiteral.class);
                    if (stringLiteral != null) {
                        reqName = stringLiteral.getValue();
                    } else {
                        // Fallback to name resolution
                        reqName = AstUtil.safeResolveName(el);
                    }
                }
                
                if (reqName != null) {
                    _classRequires.add(reqName);
                    Reference ref = addReference(reqName, ReferenceType.ClassRequire, el);
                    if(ref != null) {
                        ref.setReferenceNode(requires);
                    }
                }
            }
        } else if(req instanceof StringLiteral) {
            reqName = ((StringLiteral) req).getValue();
            _classRequires.add(reqName);
            Reference ref = addReference(reqName, ReferenceType.ClassRequire, req);
            if(ref != null) {
                ref.setReferenceNode(requires);
            }
        } else {
            // FIXED: Try safe resolution for passthrough nodes
            reqName = AstUtil.safeResolveName(req);
            if (reqName != null) {
                _classRequires.add(reqName);
                Reference ref = addReference(reqName, ReferenceType.ClassRequire, req);
                if(ref != null) {
                    ref.setReferenceNode(requires);
                }
            } else {
                CompilerMessage.UnexpectedNodeType.log(req, "Expected String or Array");
            }
        }
    }
}

// Fix 5: initMixins method - Enhanced mixin handling
public void initMixins(ObjectProperty mixins) {
    if (mixins != null) {
        BaseNode mixin = mixins.getRight();
        
        if (mixin instanceof ArrayLiteral) {
            for (BaseNode el : ((ArrayLiteral) mixin).getElements()) {
                String mname = null;
                if(el instanceof StringLiteral) {
                    mname = ((StringLiteral) el).getValue();
                } else if(el instanceof PropertyGet) {
                    mname = AstUtil.safeResolveName(el); // FIXED: Use safeResolveName
                } else {
                    // FIXED: Try safe resolution for passthrough nodes
                    mname = AstUtil.safeResolveName(el);
                }
                
                if(mname != null) {
                    _classMixins.add(mname);
                    addReference(mname, ReferenceType.ClassMixin, el);
                }
            }
        } else if(mixin instanceof ObjectLiteral) {
            _namedMixins = true;
            for (ObjectProperty el : ((ObjectLiteral) mixin).getElements()) {
                String mname = AstUtil.safeResolveName(el.getRight()); // FIXED: Use safeResolveName
                if (mname != null) {
                    _classMixins.add(mname);
                    addReference(mname, ReferenceType.ClassMixin, el.getRight());
                }
            }
        } else {
            // FIXED: Handle passthrough nodes for mixins
            Map<String, BaseNode> mixinProps = AstUtil.safeGetObjectProperties(mixin);
            if (!mixinProps.isEmpty()) {
                _namedMixins = true;
                for (Map.Entry<String, BaseNode> entry : mixinProps.entrySet()) {
                    String mname = AstUtil.safeResolveName(entry.getValue());
                    if (mname != null) {
                        _classMixins.add(mname);
                        addReference(mname, ReferenceType.ClassMixin, entry.getValue());
                    }
                }
            } else {
                CompilerMessage.UnexpectedNodeType.log(mixin, "Expected Array or Object");
            }
        }
    }
}
