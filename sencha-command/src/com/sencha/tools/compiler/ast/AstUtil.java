/*
 * Copyright (c) 2012. Sencha Inc.
 */


package com.sencha.tools.compiler.ast;

import com.sencha.tools.compiler.ast.ClosureParserUtil;
import com.sencha.tools.compiler.ast.SimpleClosureConverter;
import com.google.javascript.jscomp.parsing.parser.SourceFile;
import com.google.javascript.jscomp.parsing.parser.trees.ProgramTree;
import com.google.javascript.jscomp.parsing.parser.util.ErrorReporter;
import com.google.javascript.jscomp.parsing.parser.util.SourcePosition;
import com.sencha.command.environment.BuildEnvironment;
import com.sencha.exceptions.ExParse;
import com.sencha.logging.SenchaLogManager;
import com.sencha.tools.compiler.CompilerMessage;
import com.sencha.tools.compiler.ast.js.*;
import com.sencha.tools.compressors.JsLanguageLevel;
import com.sencha.util.JsonObjectHelper;
import com.sencha.util.PathUtil;
import com.sencha.util.StringUtil;
import org.mozilla.javascript.CompilerEnvirons;
import org.mozilla.javascript.Parser;
import org.mozilla.javascript.Token;
import org.mozilla.javascript.ast.AstNode;
import org.mozilla.javascript.ast.AstRoot;
import org.slf4j.Logger;

import java.io.File;
import java.util.*;
import java.util.Map.Entry;

public class AstUtil {
    private static final Logger _logger = SenchaLogManager.getLogger();

    private static ThreadLocal<CompilerEnvirons> _defaultEnvirons = new InheritableThreadLocal<CompilerEnvirons>(){};

    static {
        setupDefaultEnviorns(false);
    }

    private static class NameVisitor extends OptimizedNodeVisitor<Object> {
        private StringBuilder _builder = new StringBuilder();

        public void addName(String name) {
            if (_builder.length() > 0) {
                _builder.append(".");
            }
            _builder.append(name);
        }

        @Override
        public void onName(Name name) {
            addName(name.getValue());
        }

        @Override
        public void onLabel(Label label) {
            addName(label.getName());
        }

        @Override
        public void onStringLiteral(StringLiteral lit) {
            addName(lit.getValue());
        }

        @Override
        public void onKeywordLiteral (KeywordLiteral lit) {
            addName(lit.getValue());
        }

        @Override
        public void onNumberLiteral (NumberLiteral lit) {
            addName(lit.getStringValue());
        }

        @Override
        public void onElementGet(ElementGet elem) {
            this.visit(elem.getTarget());
        }

        public String getName(BaseNode node) {
            _builder.setLength(0);
            this.visit(node);
            return _builder.toString();
        }
    }

    public static String resolveName (BaseNode node) {
        NameVisitor vis = new NameVisitor();
        return vis.getName(node);
    }

    /**
     * Enhanced resolveName method that handles PassthroughNode and SimplePassthroughNode instances.
     * If the node is a passthrough node, it attempts to parse the original source
     * and extract the name from the parsed content.
     *
     * @param node The node to resolve name from
     * @return The resolved name or null if resolution fails
     */
    public static String safeResolveName(BaseNode node) {
        if (node == null) {
            return null;
        }

        // Handle SimplePassthroughNode
        if (node instanceof SimplePassthroughNode) {
            SimplePassthroughNode passthrough = (SimplePassthroughNode) node;
            String originalSource = passthrough.getOriginalSource();

            if (originalSource != null && !originalSource.trim().isEmpty()) {
                try {
                    // Try to parse the original source and resolve name
                    RootNode parsed = parse(originalSource);
                    BaseNode extracted = extractFirstMeaningfulNode(parsed);
                    if (extracted != null) {
                        return resolveName(extracted);
                    }
                } catch (Exception e) {
                    // Parsing failed, return the original source as fallback
                    return originalSource.trim();
                }
            }
            return null;
        }

        // Handle PassthroughNode
        if (node instanceof PassthroughNode) {
            PassthroughNode passthrough = (PassthroughNode) node;
            String originalSource = passthrough.getOriginalSyntax();

            if (originalSource != null && !originalSource.trim().isEmpty()) {
                try {
                    // Try to parse the original source and resolve name
                    RootNode parsed = parse(originalSource);
                    BaseNode extracted = extractFirstMeaningfulNode(parsed);
                    if (extracted != null) {
                        return resolveName(extracted);
                    }
                } catch (Exception e) {
                    // Parsing failed, return the original source as fallback
                    return originalSource.trim();
                }
            }
            return null;
        }

        // Use standard resolveName for regular nodes
        try {
            return resolveName(node);
        } catch (Exception e) {
            // If standard resolution fails, return null
            return null;
        }
    }


    public static RootNode parseRhino (
            String data,
            CompilerEnvirons env,
            final String uri,
            int lineNo) {
        Parser parser = new Parser(env, SenchaErrorReporter.DefaultReporter);
        AstRoot root = parser.parse(data, uri, lineNo);
        return (RootNode) convert(root);
    }

    public static RootNode parseRhino(String source) {
        return parseRhino(source, getDefaultEnvirons(), null, 0);
    }

    public static JsLanguageLevel InputLevel = JsLanguageLevel.NEXT;

    public static RootNode parseClosure (
            String data,
            final String uri) {
        return parseClosure(data, uri, InputLevel);
    }

    public static RootNode parseClosure (
            String data,
            final String uri,
            JsLanguageLevel level) {

        if (level == null) {
            level = InputLevel;
        }

        SourceFile file = new SourceFile(uri, data);

        ErrorReporter reporter = new ErrorReporter() {
            @Override
            protected void reportError(SourcePosition location, String message) {
                CompilerMessage.ClosureError.log(location.source.name, location.line + 1, location.column, message);
            }

            @Override
            protected void reportWarning(SourcePosition location, String message) {
                CompilerMessage.ClosureWarn.log(location.source.name, location.line + 1, location.column, message);
            }
        };

        com.google.javascript.jscomp.parsing.parser.Parser p =
                new com.google.javascript.jscomp.parsing.parser.Parser(level.getClosureParserConfig(), reporter, file);

        SimpleClosureConverter converter = new SimpleClosureConverter();
        converter.setSourceName(uri);

        // Set the original source data
        converter.setOriginalSource(data);
        
        return (RootNode) converter.doConvert(p.parseProgram(), null);
    }

    // public static ProgramTree parseClosureOnly(String data, String uri, JsLanguageLevel level) {
    //     SourceFile file = new SourceFile(uri, data);
        
    //     ErrorReporter reporter = new ErrorReporter() {
    //         @Override
    //         protected void reportError(SourcePosition location, String message) {
    //             // Handle errors as needed
    //         }
            
    //         @Override
    //         protected void reportWarning(SourcePosition location, String message) {
    //             // Handle warnings as needed
    //         }
    //     };
        
    //     Parser parser = new Parser(level.getClosureParserConfig(), reporter, file);
    //     return parser.parseProgram(); // Returns raw ProgramTree without conversion
    // }

    public static ProgramTree parseClosureOnly(String data, String uri, JsLanguageLevel level) {
        return ClosureParserUtil.parseOnly(data, uri, level);
    }

    public static RootNode parseClosure(String source) {
        return parseClosure(source, null);
    }

    public static RootNode parse (String data, CompilerEnvirons env, final String uri, int lineNo) {
        return parse(data, env, uri, lineNo, InputLevel);
    }

    public static RootNode parse (String data, CompilerEnvirons env, final String uri, int lineNo, JsLanguageLevel level) {
        if (level == null) {
            level = InputLevel;
        }

        if (level.isES6orGreater()) {
            return parseClosure(data, uri);
        }
        else {
            return parseRhino(data, env, uri, lineNo);
        }
    }

    public static RootNode parse (String data) {
        return parse(data, "unknown-uri");
    }

    public static RootNode parse (String data, String uri) {
        return parse(data, getDefaultEnvirons(), uri, 0);
    }

    public static RootNode parse (String data, File file) {
        return parse(data, getDefaultEnvirons(), PathUtil.getCanonicalPath(file), 0);
    }

    public static RootNode parse (String data, CompilerEnvirons env) {
        return parse(data, env, "unknown-uri", 0);
    }

    public static RootNode parse (
        String data,
        String filename,
        CompilerEnvirons env) {
        return parse(data, env, filename, 0);
    }

    public static RootNode parse (
        String data,
        String filename,
        CompilerEnvirons env,
        JsLanguageLevel level) {
        return parse(data, env, filename, 0, level);
    }

    public static BaseNode convert(AstNode node) {
        BasicRhinoConverter converter = new BasicRhinoConverter();
        RootNode root = (RootNode) converter.doConvert(node, null);
        return root;
    }


    public static String toSource(BaseNode node) {
        return toSource(node, true, false, new StringBuilder());
    }

    public static String toSource(BaseNode node, String newLine) {
        return toSource(node, true, false, new StringBuilder(), null, newLine);
    }

    public static String toSource(BaseNode node, boolean pretty) {
        return toSource(node, pretty, false, new StringBuilder());
    }

    public static String toSource(BaseNode node, BuildEnvironment be) {
        return toSource(node, true, false, new StringBuilder(), be, null);
    }

    public static String toSource(BaseNode node, boolean pretty, BuildEnvironment be) {
        return toSource(node, pretty, false, new StringBuilder(), be, null);
    }

    public static String toSource(BaseNode node, BuildEnvironment be, String newLine) {
        return toSource(node, true, false, new StringBuilder(), be, newLine);
    }

    public static String toSource(BaseNode node, boolean pretty, BuildEnvironment be, String newLine) {
        return toSource(node, pretty, false, new StringBuilder(), be, newLine);
    }

    public static String toSource(
        BaseNode node,
        final boolean pretty,
        final boolean stripComments) {
        return toSource(node, pretty, stripComments, new StringBuilder());
    }

    public static String toSource(BaseNode node, StringBuilder builder) {
        return toSource(node, true, false, builder);
    }

    public static String toSource(BaseNode node, boolean pretty, StringBuilder builder) {
        return toSource(node, pretty, false, builder);
    }

    public static String toSource(
        BaseNode node,
        final boolean pretty,
        final boolean stripComments,
        StringBuilder builder) {
//        return toSource(node, pretty, stripComments, builder, BuildEnvironment.load(new Configuration(), Locator.getBaseDir()));
        return toSource(node, pretty, stripComments, builder, null, null);
    }

    public static String toSource(
        BaseNode node,
        final boolean pretty,
        final boolean stripComments,
        StringBuilder builder,
        BuildEnvironment ce,
        String newLine) {
        SourceBuilder printer = new SourceBuilder(){{
            setPretty(pretty);
            setStripComments(stripComments);
        }};
        if (!StringUtil.isNullOrEmpty(newLine)) {
            printer.setNewLine(newLine);
        }
        if(ce != null) {
            printer.setUnicodeEscapes(ce.getUnicodeEscapes());
        }
        return printer.print(node, builder);
    }

    public static Map<String, BaseNode> getObjectProperties(ObjectLiteral obj) {
        Map<String, BaseNode> props = new LinkedHashMap<String, BaseNode>();
        if(obj != null) {
            for(ObjectProperty prop : obj.getElements()) {
                props.put(resolveName(prop.getLeft()), prop.getValue());
            }
        }
        return props;
    }

    /**
     * Enhanced getObjectProperties method that handles PassthroughNode and SimplePassthroughNode instances.
     * If the obj parameter is a passthrough node, it attempts to parse the original source
     * and extract the ObjectLiteral from the parsed content.
     *
     * @param obj The ObjectLiteral or passthrough node to extract properties from
     * @return A map of property names to their values
     */
    public static Map<String, BaseNode> safeGetObjectProperties(BaseNode obj) {
        Map<String, BaseNode> props = new LinkedHashMap<String, BaseNode>();

        if (obj == null) {
            return props;
        }

        // Direct handling for ObjectLiteral
        if (obj instanceof ObjectLiteral) {
            return getObjectProperties((ObjectLiteral) obj);
        }

        // Handle SimplePassthroughNode
        if (obj instanceof SimplePassthroughNode) {
            SimplePassthroughNode passthrough = (SimplePassthroughNode) obj;
            String originalSource = passthrough.getOriginalSource();

            if (originalSource != null && !originalSource.trim().isEmpty()) {
                try {
                    // Try to parse the original source and extract ObjectLiteral
                    RootNode parsed = parse(originalSource);
                    BaseNode extracted = extractNodeOfType(parsed, ObjectLiteral.class);
                    if (extracted instanceof ObjectLiteral) {
                        return getObjectProperties((ObjectLiteral) extracted);
                    }
                } catch (Exception e) {
                    // Parsing failed, return empty map
                }
            }
            return props;
        }

        // Handle PassthroughNode
        if (obj instanceof PassthroughNode) {
            PassthroughNode passthrough = (PassthroughNode) obj;
            String originalSource = passthrough.getOriginalSyntax();

            if (originalSource != null && !originalSource.trim().isEmpty()) {
                try {
                    // Try to parse the original source and extract ObjectLiteral
                    RootNode parsed = parse(originalSource);
                    BaseNode extracted = extractNodeOfType(parsed, ObjectLiteral.class);
                    if (extracted instanceof ObjectLiteral) {
                        return getObjectProperties((ObjectLiteral) extracted);
                    }
                } catch (Exception e) {
                    // Parsing failed, return empty map
                }
            }
            return props;
        }

        // For other node types, return empty map
        return props;
    }
    
    public static void addSimplePropertyToObject (
        ObjectLiteral obj, String name, String value) {

        Name nameNode = new Name();
        nameNode.setValue(name);
        StringLiteral valueNode = new StringLiteral();
        
        valueNode.setQuoteCharacter('\'');
        
        valueNode.setValue(value);
        ObjectProperty prop = new ObjectProperty();
        prop.setLeft(nameNode);
        prop.setRight(valueNode);
        obj.addElement(prop);
    }

    public static void applySimplePropertyToObject(
        ObjectLiteral obj, String name, String value){

        Map<String, BaseNode> props = getObjectProperties(obj);
        if(!props.containsKey(name)) {
            addSimplePropertyToObject(obj, name, value);
        }

    }

    public static Map<String, BaseNode> merge(Map<String, BaseNode> objA, Map<String, BaseNode> objB){
        return new JsonObjectHelper().override(objA, objB);
    }


    public static Map<String, BaseNode> sort(Map<String, BaseNode> props) {
        LinkedHashMap<String, BaseNode> output = new LinkedHashMap<String, BaseNode>();
        List<String> keys = new LinkedList<String>(props.keySet());

        Collections.sort(keys, new Comparator<String>() {
            @Override
            public int compare (String o1, String o2) {
                return o1.compareTo(o2);
            }
        });

        for(String key : keys) {
            output.put(key, props.get(key));
        }

        return output;
    }

    public static ObjectLiteral merge(ObjectLiteral dst, ObjectLiteral src) {
        Map<String, BaseNode> srcProps = getObjectProperties(src),
                             dstProps = getObjectProperties(dst),
                             finalMap = merge(dstProps, srcProps);

        return createObjectLiteral(finalMap);
    }

    public static ObjectLiteral createObjectLiteral(Map<String, BaseNode> props) {
        ObjectLiteral obj = new ObjectLiteral();
        for(Entry<String, BaseNode> entry : props.entrySet()) {
            ObjectProperty prop = createProperty(entry.getKey(), entry.getValue());
            obj.addElement(prop);
        }
        return obj;
    }

    public static CompilerEnvirons createEnvirons(boolean safe) {
        CompilerEnvirons env = new CompilerEnvirons();
        env.setRecordingComments(true);
        env.setWarnTrailingComma(true);
        if (safe) {
            env.setRecoverFromErrors(true);
            env.setIdeMode(true);
        } else {
            env.setRecoverFromErrors(false);
            env.setIdeMode(false);
        }
        return env;
    }

    public static CompilerEnvirons getDefaultEnvirons() {
        if (_defaultEnvirons.get() == null) {
            _defaultEnvirons.set(createEnvirons(false));
        }
        return _defaultEnvirons.get();
    }

    public static void setupDefaultEnviorns(boolean safe) {
        _defaultEnvirons.set(createEnvirons(safe));
    }

    public static boolean replaceFunctionCall(FunctionCall oldCall, FunctionCall newCall) {
        BaseNode parent = oldCall.getParent();
        newCall.setParent(parent);
        boolean replaced = false;
        
        if(parent instanceof ExpressionStatement) {
            ExpressionStatement statement = (ExpressionStatement)parent;
            statement.setExpression(newCall);
            replaced = true;
        } else if(parent instanceof VariableInitializer) {
            VariableInitializer init = (VariableInitializer)parent;
            if(init.getInitializer() == oldCall) {
                init.setInitializer(newCall);
                replaced = true;
            }
        } else if (parent instanceof Infix){
            Infix expr = (Infix) parent;
            if(expr.getLeft() == oldCall) {
                expr.setLeft(newCall);
                replaced = true;
            } else if(expr.getRight() == oldCall) {
                expr.setRight(newCall);
                replaced = true;
            }
        } else if(parent instanceof ReturnStatement){
            ReturnStatement ret = (ReturnStatement)parent;
            ret.setReturnValue(newCall);
            replaced = true;
        } else if(parent instanceof FunctionCall){
            List<BaseNode> args = ((FunctionCall)parent).getArguments();
            for(int x=0; x<args.size(); x++) {
                BaseNode arg = args.get(x);
                if(arg == oldCall) {
                    args.set(x, newCall);
                    replaced = true;
                }
            }
        } else if(parent instanceof ConditionalExpression){
            ConditionalExpression expr = (ConditionalExpression)parent;
            if(expr.getTest() == oldCall) {
                expr.setTest(newCall);
                replaced = true;
            }
            if(expr.getTrue() == oldCall) {
                expr.setTrue(oldCall);
                replaced = true;
            }
            if(expr.getFalse() == oldCall) {
                expr.setFalse(newCall);
                replaced = true;
            }
        } else if(parent instanceof Unary) {
            Unary unary = (Unary)parent;
            unary.setOperand(newCall);
            replaced = true;
        } else {
            if(_logger.isDebugEnabled()) {
                _logger.error("Parent : {}", parent.getClass().getName());
            }
        }
        
        return replaced;
    }


    public static <T> T cast(BaseNode node, Class<T> clazz) {
        if(!clazz.isAssignableFrom(node.getClass())) {
            String message = StringUtil.formatTemplate("Expected {0} but found {1} ({2})", clazz.getSimpleName(), node, node.getClass().getSimpleName());
            CompilerMessage.CastError.log(node, message);
            throw new ExParse(message);
        }
        return (T) node;
    }

    /**
     * Safe cast method that handles PassthroughNode and SimplePassthroughNode instances.
     * If the node is a passthrough node, it attempts to parse the original source
     * and extract the expected node type.
     *
     * @param node The node to cast
     * @param clazz The expected class type
     * @return The cast node or null if casting fails
     */
    public static <T> T safeCast(BaseNode node, Class<T> clazz) {
        if (node == null) {
            return null;
        }

        // Direct cast if possible
        if (clazz.isAssignableFrom(node.getClass())) {
            return (T) node;
        }

        // Handle SimplePassthroughNode
        if (node instanceof SimplePassthroughNode) {
            SimplePassthroughNode passthrough = (SimplePassthroughNode) node;
            String originalSource = passthrough.getOriginalSource();

            if (originalSource != null && !originalSource.trim().isEmpty()) {
                try {
                    // Try to parse the original source and extract the expected type
                    RootNode parsed = parse(originalSource);
                    BaseNode extracted = extractNodeOfType(parsed, clazz);
                    if (extracted != null && clazz.isAssignableFrom(extracted.getClass())) {
                        return (T) extracted;
                    }
                } catch (Exception e) {
                    // Parsing failed, fall through to error handling
                }
            }
        }

        // Handle PassthroughNode
        if (node instanceof PassthroughNode) {
            PassthroughNode passthrough = (PassthroughNode) node;
            String originalSource = passthrough.getOriginalSyntax();

            if (originalSource != null && !originalSource.trim().isEmpty()) {
                try {
                    // Try to parse the original source and extract the expected type
                    RootNode parsed = parse(originalSource);
                    BaseNode extracted = extractNodeOfType(parsed, clazz);
                    if (extracted != null && clazz.isAssignableFrom(extracted.getClass())) {
                        return (T) extracted;
                    }
                } catch (Exception e) {
                    // Parsing failed, fall through to error handling
                }
            }
        }

        // If we can't cast, return null instead of throwing exception
        return null;
    }

    /**
     * Safe cast method that handles PassthroughNode and SimplePassthroughNode instances.
     * If the node is a passthrough node and casting fails, returns the fallback value.
     *
     * @param node The node to cast
     * @param clazz The expected class type
     * @param fallback The fallback value to return if casting fails
     * @return The cast node or fallback value
     */
    public static <T> T safeCast(BaseNode node, Class<T> clazz, T fallback) {
        T result = safeCast(node, clazz);
        return result != null ? result : fallback;
    }

    /**
     * Extracts a node of the specified type from a parsed AST.
     * This is a helper method for safeCast to find the expected node type
     * within a parsed AST tree.
     *
     * @param root The root node to search
     * @param clazz The expected class type
     * @return The first node of the expected type, or null if not found
     */
    private static <T> BaseNode extractNodeOfType(BaseNode root, Class<T> clazz) {
        if (root == null) {
            return null;
        }

        // Check if the root itself is the expected type
        if (clazz.isAssignableFrom(root.getClass())) {
            return root;
        }

        // For RootNode, check the first element
        if (root instanceof RootNode) {
            RootNode rootNode = (RootNode) root;
            if (!rootNode.getElements().isEmpty()) {
                BaseNode firstElement = rootNode.getElements().get(0);

                // Check if first element is the expected type
                if (clazz.isAssignableFrom(firstElement.getClass())) {
                    return firstElement;
                }

                // For ExpressionStatement, check the expression
                if (firstElement instanceof ExpressionStatement) {
                    ExpressionStatement stmt = (ExpressionStatement) firstElement;
                    BaseNode expr = stmt.getExpression();
                    if (expr != null && clazz.isAssignableFrom(expr.getClass())) {
                        return expr;
                    }
                }
            }
        }

        // For ExpressionStatement, check the expression
        if (root instanceof ExpressionStatement) {
            ExpressionStatement stmt = (ExpressionStatement) root;
            BaseNode expr = stmt.getExpression();
            if (expr != null && clazz.isAssignableFrom(expr.getClass())) {
                return expr;
            }
        }

        return null;
    }

    /**
     * Extracts the first meaningful node from a parsed AST.
     * This is a helper method for safeResolveName to find the actual content
     * within a parsed AST tree.
     *
     * @param root The root node to search
     * @return The first meaningful node, or null if not found
     */
    private static BaseNode extractFirstMeaningfulNode(BaseNode root) {
        if (root == null) {
            return null;
        }

        // For RootNode, get the first element
        if (root instanceof RootNode) {
            RootNode rootNode = (RootNode) root;
            if (!rootNode.getElements().isEmpty()) {
                BaseNode firstElement = rootNode.getElements().get(0);

                // For ExpressionStatement, get the expression
                if (firstElement instanceof ExpressionStatement) {
                    ExpressionStatement stmt = (ExpressionStatement) firstElement;
                    return stmt.getExpression();
                }

                return firstElement;
            }
        }

        // For ExpressionStatement, get the expression
        if (root instanceof ExpressionStatement) {
            ExpressionStatement stmt = (ExpressionStatement) root;
            return stmt.getExpression();
        }

        return root;
    }

    public static <T extends BaseNode> T getFirstNode(String source, Class<T> cls) {
        RootNode root = (RootNode) parse(source);
        ExpressionStatement stmt = (ExpressionStatement) root.getElements().get(0);
        BaseNode result = stmt.getExpression();
        return (T)result;
    }

    public static ObjectProperty createProperty(String name, BaseNode node) {
        ObjectProperty prop = new ObjectProperty();
        Name propName = new Name();
        propName.setValue(name);
        prop.setLeft(propName);
        prop.setRight(node);
        return prop;
    }

    public static ObjectProperty createProperty(String name, String value) {
        StringLiteral str = new StringLiteral();
        str.setQuoteCharacter('\'');
        str.setValue(value);
        return createProperty(name, str);
    }

    public static ObjectProperty createProperty(String name, boolean value) {
        KeywordLiteral lit = new KeywordLiteral();
        lit.setCode(value ? Token.TRUE : Token.FALSE);
        return createProperty(name, lit);
    }
}
