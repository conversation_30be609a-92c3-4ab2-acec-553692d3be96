/*
 * Copyright (c) 2012-2014. Sencha Inc.
 */

package com.sencha.tools.compiler.ast.js;

/**
 * A simplified passthrough node that preserves the original source code without transformation.
 * This is used for non-essential JavaScript constructs that don't need detailed AST parsing
 * for basic property checks (extend, mixins, requires, etc.).
 *
 * <p>This node is designed to simplify AST representation by treating most JavaScript
 * constructs as opaque blocks that preserve their original source text. This is particularly
 * useful when only specific properties need to be analyzed.</p>
 *
 * <p>Unlike PassthroughNode which is used for modern syntax preservation, SimplePassthroughNode
 * is used for general AST simplification where detailed parsing is not required.</p>
 *
 * @since 7.0.0
 */
public class SimplePassthroughNode extends BaseNode {

    private String originalSource;
    private String nodeType; // For debugging/logging purposes

    /**
     * Creates a new empty SimplePassthroughNode.
     */
    public SimplePassthroughNode() {
        this.originalSource = "";
        this.nodeType = "unknown";
    }

    /**
     * Creates a new SimplePassthroughNode with the original source text.
     *
     * @param originalSource The original source code to preserve
     */
    public SimplePassthroughNode(String originalSource) {
        this.originalSource = originalSource != null ? originalSource : "";
        this.nodeType = "unknown";
    }

    /**
     * Creates a new SimplePassthroughNode with source and type information.
     *
     * @param originalSource The original source code to preserve
     * @param nodeType The type of the original node for debugging
     */
    public SimplePassthroughNode(String originalSource, String nodeType) {
        this.originalSource = originalSource != null ? originalSource : "";
        this.nodeType = nodeType != null ? nodeType : "unknown";
    }

    /**
     * Gets the original source code that this node preserves.
     *
     * @return The original source text
     */
    public String getOriginalSource() {
        return originalSource;
    }

    /**
     * Sets the original source code for this node.
     *
     * @param originalSource The source code to preserve
     */
    public void setOriginalSource(String originalSource) {
        this.originalSource = originalSource != null ? originalSource : "";
    }

    /**
     * Gets the node type for debugging purposes.
     *
     * @return The node type string
     */
    public String getNodeType() {
        return nodeType;
    }

    /**
     * Sets the node type for debugging purposes.
     *
     * @param nodeType The node type string
     */
    public void setNodeType(String nodeType) {
        this.nodeType = nodeType != null ? nodeType : "unknown";
    }

    /**
     * Gets the length of the original source text.
     *
     * @return The length of the source, or 0 if source is null
     */
    public int getSourceLength() {
        return originalSource != null ? originalSource.length() : 0;
    }

    /**
     * Checks if this node has any source content.
     *
     * @return true if the node has non-empty source content
     */
    public boolean hasContent() {
        return originalSource != null && !originalSource.trim().isEmpty();
    }

    @Override
    public String toString() {
        return String.format("SimplePassthroughNode[type=%s, source='%s']",
                           nodeType,
                           originalSource != null ? originalSource : "null");
    }

    @Override
    public boolean equals(Object obj) {
        if (this == obj) return true;
        if (obj == null || getClass() != obj.getClass()) return false;

        SimplePassthroughNode that = (SimplePassthroughNode) obj;

        if (originalSource != null ? !originalSource.equals(that.originalSource) : that.originalSource != null) {
            return false;
        }
        if (nodeType != null ? !nodeType.equals(that.nodeType) : that.nodeType != null) {
            return false;
        }

        return true;
    }

    @Override
    public int hashCode() {
        int result = originalSource != null ? originalSource.hashCode() : 0;
        result = 31 * result + (nodeType != null ? nodeType.hashCode() : 0);
        return result;
    }

    /**
     * Implementation of the abstract doVisit method from BaseNode.
     * This method is called when a visitor visits this node.
     *
     * @param vis The node visitor
     * @param <T> The visitor's context type
     */
    @Override
    <T> void doVisit(NodeVisitor<T> vis) {
        vis.onSimplePassthroughNode(this);
    }

    /**
     * Implementation of the abstract descend method from BaseNode.
     * Since SimplePassthroughNode preserves original source without transformation,
     * it has no child nodes to visit.
     *
     * @param vis The node visitor
     * @param <T> The visitor's context type
     */
    @Override
    public <T> void descend(NodeVisitor<T> vis) {
        // SimplePassthroughNode has no child nodes to visit since it preserves
        // the original source text without detailed AST transformation
    }
}
