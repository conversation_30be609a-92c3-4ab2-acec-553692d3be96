/*
 * Copyright (c) 2012-2014. Sencha Inc.
 */

package com.sencha.tools.compiler.ast;

import com.sencha.command.environment.UnicodeEscapes;
import com.sencha.tools.compiler.ast.js.*;
import com.sencha.tools.compiler.builder.optimizer.NameOptimization;
import com.sencha.util.StringUtil;

import java.util.*;

public class SourceBuilder<T> extends OptimizedNodeVisitor<T> {
    protected StringBuilder _stringBuilder = new StringBuilder();
    protected int _indentLevel = 0;
    private String _indentStr = "    ";
    private String _currIndent = "";
    private boolean _pretty = true;
    private boolean _stripComments = false;
    private UnicodeEscapes _unicodeEscapes;
    private String _newLine = StringUtil.NewLine;
    private int _lastWrap = 0;

    public static int WrapLines = 0;
    private int _wrapLines = WrapLines;

    public String print(BaseNode node) {
        return print(node, new StringBuilder());
    }

    public String print(BaseNode node, StringBuilder builder) {
        _stringBuilder = builder;
        visit(node);
        return _stringBuilder.toString();
    }

    public String getIndentStr() {
        return _indentStr;
    }

    public void setIndentStr(String indentStr) {
        _indentStr = indentStr;
    }

    public boolean getPretty() {
        return _pretty;
    }

    public void setPretty(boolean pretty) {
        _pretty = pretty;
    }

    public boolean getStripComments() {
        return _stripComments;
    }

    public void setStripComments(boolean stripComments) {
        _stripComments = stripComments;
    }

    private void updateIndent() {
        _currIndent = "";
        for(int x = 0; x < _indentLevel; x++) {
            _currIndent += getIndentStr();
        }
    }

    public SourceBuilder<T> indent() {
        _indentLevel++;
        updateIndent();
        return this;
    }

    public SourceBuilder<T> unindent() {
        _indentLevel--;
        updateIndent();
        return this;
    }

    public SourceBuilder<T> indentLn() {
        if(_pretty) {
            return indent().addLn();
        }
        return this;
    }

    public SourceBuilder<T> unindentLn() {
        if(_pretty) {
            return unindent().addLn();
        }
        return this;
    }

    public SourceBuilder<T> tab() {
        if(_pretty) {
            return append(_currIndent);
        }
        return this;
    }

    public SourceBuilder<T> addLn() {
        if(_pretty) {
            return append(getNewLine()).tab();
        }
        else if (WrapLines > 0) {
            int len = _stringBuilder.length();
            if ((len - _lastWrap) > _wrapLines) {
                // don't use new line here, as multi-char
                // newlines will make cross-platform unit tests fail
                append("\n");
                _lastWrap = len + 1;
            }
        }
        return this;
    }

    public SourceBuilder<T> append(String str) {
        _stringBuilder.append(str);
        return this;
    }

    public SourceBuilder<T> append(char ch) {
        _stringBuilder.append(ch);
        return this;
    }

    public SourceBuilder<T> space() {
        if(_pretty) {
            return append(" ");
        }
        return this;
    }

    public boolean endsWith(char ch) {
        if(_stringBuilder.length() == 0) {
            return false;
        }
        return _stringBuilder.charAt(_stringBuilder.length() - 1) == ch;
    }

    public SourceBuilder<T> terminate(char ch) {
        if(!endsWith(ch)) {
            append(ch);
        }
        return this;
    }

    private void handleBlockOrStatement(BaseNode node) {
        if(node instanceof Block || node instanceof Scope) {
            visit(node);
        } else {
            space().append("{");
            indent().addLn();
            visit(node);
            terminate(';');
            unindent().addLn().append("}").addLn();
        }
    }
    private void handleBlockNode(NodeContainer<BaseNode> node) {
        append("{").indent();
        int start = _stringBuilder.length();
        for(BaseNode child : node.getElements()) {
            addLn();
            handleBodyStatement(child);
        }
        int end = _stringBuilder.length();
        unindent();
        if (!_pretty && _stringBuilder.charAt(_stringBuilder.length() -1) == ';') {
            _stringBuilder.setLength(_stringBuilder.length() - 1);
        }
        if(end > start) {
            addLn();
        }
        append("}");
    }

    private void handleLoopBody(BaseNode body) {
        if(body == null || body instanceof EmptyExpression) {
            append("{}");
        } else {
            space();
            visit(body);
        }
    }

    private void handleBodyStatement(BaseNode statement) {
        visit(statement);
    }
    private void printCsv(List nodes) {
        printCsv(nodes, false, true);
    }

    /**
     * handles printing comma separated lists of nodes (Object and Array Literals)
     * also handles the potential for certain optimizations to have replaces certain
     * nodes with ExmptyExpression nodes
     * @param nodes
     */
    private void printCsv(List nodes, boolean preserveEmptyNodes, boolean newLines) {
        indent();
        int arrayStart = _stringBuilder.length();
        int size = nodes.size();
        int length = _stringBuilder.length();
        int start;
        boolean addLine = newLines;
        for(int x = 0; x < size; x++) {
            start = _stringBuilder.length();
            if(addLine) {
                addLn();
            }
            // capture the current length of the string builder, as EmptyExpression
            // optimized nodes will basically leave the string builder at it's current
            // position.  this allow detecting when an EmptyExpression has been encountered
            // as the string builder won't have changed.
            length = _stringBuilder.length();
            BaseNode node = (BaseNode)nodes.get(x);
            visit(node);

            // if there are more elements to do, determine how to terminate
            // the current one
            if(x < size - 1) {
                if(_stringBuilder.length() > length) {
                    append(",");
                    addLine = newLines;
                } else if (preserveEmptyNodes) {
                    append(",");
                } else if(addLine) {
                    _stringBuilder.setLength(start);
                }
            }

            // if this is the last element, might need to remove an extra blank line
            else if(_stringBuilder.length() == length) {
                _stringBuilder.setLength(start);
            }
        }

        // remove a trailing comma, if there is one
        if(_stringBuilder.charAt(_stringBuilder.length() - 1) == ',') {
            _stringBuilder.setLength(_stringBuilder.length() - 1);
        }
        unindent();

        // if there were valid elements in the list, add a new line
        if((_stringBuilder.length() > arrayStart) && newLines) {
            addLn();
        }
    }

    private static final Set<Class> _nonStatementClasses = new HashSet<Class>(){{
        add(Loop.class);
        add(ForLoop.class);
        add(DoLoop.class);
        add(WhileLoop.class);
        add(ForInLoop.class);
        add(SwitchStatement.class);
        add(Block.class);
        add(Scope.class);
        add(IfStatement.class);
        add(TryStatement.class);
        add(FunctionNode.class);
    }};

    private SourceBuilder<T> escapeString(String s, char escapeQuote)
    {

        for(int i = 0, L = s.length(); i != L; ++i) {
            int c = s.charAt(i);

            if (' ' <= c && c != escapeQuote && c != '\\') {
                UnicodeEscapes escapes = getUnicodeEscapes();
                if(!escapes.isSet(c)) {
                    _stringBuilder.appendCodePoint(c);
                    continue;
                }
            }

            int escape = -1;
            switch (c) {
                case '\b':  escape = 'b';  break;
                case '\f':  escape = 'f';  break;
                case '\n':  escape = 'n';  break;
                case '\r':  escape = 'r';  break;
                case '\t':  escape = 't';  break;
                case 0xb:   escape = 'v';  break; // Java lacks \v.
                case ' ':   escape = ' ';  break;
                case '\\':  escape = '\\'; break;
            }
            if (escape >= 0) {
                // an \escaped sort of character
                _stringBuilder.append('\\');
                _stringBuilder.append((char) escape);
            } else if (c == escapeQuote) {
                _stringBuilder.append('\\');
                _stringBuilder.append(escapeQuote);
            } else {
                int hexSize;
                if (c < 256) {
                    // 2-digit hex
                    _stringBuilder.append("\\x");
                    hexSize = 2;
                } else {
                    // Unicode.
                    _stringBuilder.append("\\u");
                    hexSize = 4;
                }
                // append hexadecimal form of c left-padded with 0
                for (int shift = (hexSize - 1) * 4; shift >= 0; shift -= 4) {
                    int digit = 0xf & (c >> shift);
                    int hc = (digit < 10) ? '0' + digit : 'a' - 10 + digit;
                    _stringBuilder.append((char) hc);
                }
            }
        }
        return this;
    }

    @Override
    public void onAssignment(Assignment node) {
        visit(node.getLeft());
        space().append(node.getOperator().getValue()).space();
        visit(node.getRight());
    }

    @Override
    public void onPropertyGet(PropertyGet node) {
        visit(node.getLeft());
        append(".");
        visit(node.getRight());
    }

    @Override
    public void onArrayLiteral(ArrayLiteral node) {
        append("[");
        printCsv(node.getElements());
        append("]");
    }

    @Override
    public void onBlock(Block node) {
        handleBlockNode(node);
    }

    @Override
    public void onCatchClause(CatchClause node) {
        append("catch");
        space().append("(");
        visit(node.getName());
        if(node.getCondition() != null) {
            append(" if ");
            visit(node.getCondition());
        }
        append(")").space();
        visit(node.getBody());
    }

    @Override
    public void onLineComment(LineComment node) {
        if(getPretty() && !getStripComments()) {
            append(node.getValue()).addLn();
        }
    }

    @Override
    public void onBlockComment(BlockComment node) {
        if(getPretty() && !getStripComments()) {
            append(node.getValue()).addLn();
        }
    }

    @Override
    public void onConditionalExpression(ConditionalExpression node) {
        visit(node.getTest());
        space().append("?").space();
        visit(node.getTrue());
        space().append(":").space();
        visit(node.getFalse());
    }

    @Override
    public void onElementGet(ElementGet node) {
        visit(node.getTarget());
        append("[");
        visit(node.getElement());
        append("]");
    }

    @Override
    public void onEmptyExpression(EmptyExpression node) {
    }

    @Override
    public void onExpressionStatement(ExpressionStatement node) {
        visit(node.getExpression());
        terminate(';');
    }

    @Override
    public void onFunctionCall(FunctionCall node) {
        visit(node.getTarget());
        append("(");
        for(int x = 0; x < node.getArguments().size(); x++) {
            if(x > 0) {
                append(",").space();
            }
            visit(node.getArguments().get(x));
        }
        append(")");
    }

    @Override
    public void onIfStatement(IfStatement node) {
        append("if").space().append("(");
        visit(node.getCondition());
        append(")").space();
        handleBlockOrStatement(node.getThen());
        if(node.getElse() != null) {
            BaseNode elsePart = node.getElse();
            if(elsePart instanceof IfStatement) {
                Collection<Comment> comments = elsePart.getComments();
                if(comments.size() > 0) {
                    addLn();
                    for(Comment comment : elsePart.getComments()) {
                        visit(comment);
                    }
                } else if(!endsWith(' ')) {
                    space();
                }
                append("else ");
                onIfStatement((IfStatement) elsePart.getOptimized());
            } else {
                if(!endsWith(' ')) {
                    space();
                }
                append("else ");
                handleBlockOrStatement(elsePart);
            }
        }
    }

    @Override
    public void onInfix(Infix node) {
        visit(node.getLeft());
        String op = node.getOperator().getValue();
        BaseNode right = node.getRight();
        if ("in".equals(op) || "of".equals(op) || "instanceof".equals(op)) {
            append(' ').append(op).append(' ');
        }
        else {
            boolean spaceRequired = false;
            char last = '\0';
            if (_stringBuilder.length() > 0) {
                last = _stringBuilder.charAt(_stringBuilder.length() - 1);
            }
            if ((last == '+' || last == '-') && op.charAt(0) == last) {
                spaceRequired = true;
            }

            if (spaceRequired) {
                append(" ").append(op).space();
            }
            else {
                space().append(op).space();
            }
        }
        visit(right);
    }

    @Override
    public void onJumpNode(JumpNode node) {
        // noOpo
    }

    @Override
    public void onKeywordLiteral(KeywordLiteral node) {
        append(node.getValue());
    }

    @Override
    public void onLabeledStatement(LabeledStatement node) {
        for(Label lbl : node.getLabels()) {
            visit(lbl);
            append(" ");
        }
        visit(node.getStatement());
    }

    @Override
    public void onName(Name node) {
        append(node.getValue());
    }

    @Override
    public void onNumberLiteral(NumberLiteral node) {
        append(node.getStringValue());
    }

    @Override
    public void onObjectLiteral(ObjectLiteral node) {
        append("{");
        printCsv(node.getElements());
        append("}");
    }

    @Override
    public void onObjectProperty(ObjectProperty node) {
        BaseNode name = node.getName();

        // Due to YUI compressor issues, we need to quote reserved words when
        // used as keys in object literals
        if(name instanceof Name) {
            String nameVal = ((Name) name).getValue();
            if(NameOptimization.ReservedWords.contains(nameVal)) {
                append("\"" + nameVal + "\"");
            } else {
                visit(name);
            }
        } else {
            visit(name);
        }
        if (node.getValue() != null) {
            append(":").space();
            visit(node.getValue());
        }
    }

    @Override
    public void onParenthesizedExpression(ParenthesizedExpression node) {
        append("(");
        visit(node.getExpression());
        append(")");
    }

    @Override
    public void onRegExpLiteral(RegExpLiteral node) {
        append("/").append(node.getValue()).append("/");
        if(!StringUtil.isNullOrEmpty(node.getFlags())) {
            append(node.getFlags());
        }
    }

    @Override
    public void onReturnStatement(ReturnStatement node) {
        append("return");
        if(node.getReturnValue() != null) {
            append(" ");
            visit(node.getReturnValue());
        }
        terminate(';');
    }

    @Override
    public void onStringLiteral(StringLiteral node) {
        append(node.getQuoteCharacter());
        escapeString(node.getValue(), node.getQuoteCharacter());
        append(node.getQuoteCharacter());
    }

    @Override
    public void onSwitchCase(SwitchCase node) {

        BaseNode expr = node.getExpression();
        if(expr == null) {
            append("default:");
        } else {
            append("case ");
            visit(expr);
            append(":");

        }

        List<BaseNode> statements = node.getElements();
        if(statements != null) {
            indent();
            for(BaseNode statement : statements) {
                addLn();
                visit(statement);
                terminate(';');
            }
            unindent();
        }
    }

    @Override
    public void onThrowStatement(ThrowStatement node) {
        append("throw ");
        visit(node.getExpr());
        terminate(';');
    }

    @Override
    public void onTryStatement(TryStatement node) {
        append("try").space();
        visit(node.getTryBlock());
        for(BaseNode clause : node.getCatchClauses()) {
            space();
            visit(clause);
        }
        if(node.getFinallyBlock() != null) {
            space();
            append("finally").space();
            visit(node.getFinallyBlock());
        }
    }

    @Override
    public void onUnary(Unary node) {
        Operators op = node.getOperator();
        boolean postfix = node.isPostfix();
        if(!postfix) {
            char last = '\0';
            if (_stringBuilder.length() > 0) {
                last = _stringBuilder.charAt(_stringBuilder.length() - 1);
            }
            String opVal = op.getValue();
            if ((last == '+' || last == '-') && opVal.charAt(0) == last) {
                append(" ");
            }
            append(opVal);
            if(op == Operators.TYPEOF || op == Operators.DELPROP || op == Operators.VOID) {
                append(" ");
            }
        }
        visit(node.getOperand());
        if(postfix) {
            append(op.getValue());
        }
    }

    //done
    @Override
    public void onVariableDeclaration(VariableDeclaration node) {
        if (node.isLetInitializer()) {
            append("let ");
        }
        else if (node.isConst()) {
            append("const ");
        }
        else {
            append("var ");
        }
        indent();
        int count = 0;
        int size = node.getVariables().size();
        boolean lastWasInit = true;
        boolean isInit;
        for(VariableInitializer var : node.getVariables()) {
            isInit = var.getInitializer() != null;
            if(isInit && !lastWasInit) {
                addLn();
            }
            if(!isInit && !lastWasInit && count > 0) {
                space();
            }
            visit(var);
            count++;
            if(count < size) {
                append(",");
            }
            if(isInit && count < size) {
                addLn();
            }
            lastWasInit = isInit;
        }
        if(node.isStatement()) {
            terminate(';');
        }
        unindent();
    }

    @Override
    public void onVariableInitializer(VariableInitializer node) {
        visit(node.getTarget());
        if(node.getInitializer() != null) {
            space().append("=").space();
            visit(node.getInitializer());
        }
    }

    @Override
    public void onWithStatement(WithStatement node) {
        append("with").space().append("(");
        visit(node.getExpr());
        append(")");
        visit(node.getStatement());
    }

    @Override
    public void onXmlFragment(XmlFragment node) {
        // rhino's AST is a no-op for toSource on xmlfragment
    }

    @Override
    public void onXmlLiteral(XmlLiteral node) {
        for(XmlFragment f : node.getElements()) {
            visit(f);
        }
    }

    @Override
    public void onXmlRef(XmlRef node) {
        // rhino's AST is a no-op for toSource on xmlref
    }

    @Override
    public void onYieldStatement(YieldStatement node) {
        append("yield");
        if(node.getValue() != null) {
            append(" ");
            visit(node.getValue());
        }
    }

    @Override
    public void onScope(Scope node) {
        onBlock(node);
    }

    @Override
    public void onScriptNode(ScriptNode node) {
        onScope(node);
    }


    @Override
    public void onFunctionNode(FunctionNode node) {
        FunctionNode.FunctionType ftype = node.getFunctionType();
        boolean skipBraces = ftype == FunctionNode.FunctionType.Arrow &&
                (node.getParams() != null && node.getParams().size() == 1);

        List<String> parts = new ArrayList<String>();

        if (node.isStatic()) {
            parts.add("static");
        }
        if (node.isAsync()) {
            parts.add("async");
        }
        if (ftype == FunctionNode.FunctionType.Standard) {
            parts.add("function");
        }
        if (node.isGenerator()) {
            parts.add("*");
        }

        if(node.getName() != null) {
            StringBuilder buff = new StringBuilder();
            StringBuilder curr = _stringBuilder;
            _stringBuilder = buff;
            visit(node.getName());
            _stringBuilder = curr;
            parts.add(buff.toString());
        }

        append(StringUtil.join(parts, " "));
        if (node.isAsync() && node.getName() == null) {
            append(" ");
        }

        if (!skipBraces) {
            append("(");
        }
        for(int x = 0; x < node.getParams().size(); x++) {
            if(x > 0) {
                append(",");
                space();
            }
            visit(node.getParams().get(x));
        }
        if (!skipBraces) {
            append(")");
        }
        space();
        if (ftype == FunctionNode.FunctionType.Arrow) {
            append("=>");
            space();
        }
        visit(node.getBody());
    }

    @Override
    public void onBreakStatement(BreakStatement node) {
        append("break");
        if(node.getLabel() != null) {
            append(" ");
            visit(node.getLabel());
        }
        terminate(';');
    }

    @Override
    public void onContinueStatement(ContinueStatement node) {
        addLn().append("continue");
        if(node.getLabel() != null) {
            append(" ");
            visit(node.getLabel());
        }
        terminate(';');
    }

    @Override
    public void onLabel(Label node) {
        append(node.getName()).append(":");
    }

    @Override
    public void onSwitchStatement(SwitchStatement node) {
        append("switch").space().append("(");
        visit(node.getExpr());
        append(")").space().append("{");
        indent();
        for(SwitchCase sc : node.getElements()) {
            addLn();
            visit(sc);
        }
        unindent();
        addLn().append("}");
    }

    @Override
    public void onArrayComprehension(ArrayComprehension node) {
        append("[");
        visit(node.getResult());
        for(BaseNode loop : node.getLoops()) {
            visit(loop);
        }
        if(node.getFilter() != null) {
            append(" if (");
            visit(node.getFilter());
            append(")");
        }
        append("]");
    }

    @Override
    public void onLetNode(LetNode node) {
        append("let").space().append("(");
        visit(node.getVariables());
        append(")");
        if(node.getBody() != null) {
            visit(node.getBody());
        }
    }

    @Override
    public void onLoop(Loop node) {
        handleLoopBody(node.getBody());
    }

    @Override
    public void onDoLoop(DoLoop node) {
        append("do");
        if (!(node.getBody() instanceof Block) && !_pretty) {
            append(" ");
        }
        handleLoopBody(node.getBody());
        if(node.getCondition() != null) {
            space().append("while").space().append("(");
            visit(node.getCondition());
            append(");");
        }
    }

    @Override
    public void onForLoop(ForLoop node) {
        append("for").space().append("(");
        visit(node.getInitializer());
        append(";").space();
        visit(node.getCondition());
        append(";").space();
        visit(node.getIncrement());
        append(")");
        handleLoopBody(node.getBody());
    }

    @Override
    public void onForInLoop(ForInLoop node) {
        append("for");
        if(node.isForEach()) {
            append(" each");
        }
        space().append("(");
        visit(node.getIterator());
        append(" in ");
        visit(node.getIteratedObject());
        append(")").space();
        visit(node.getBody());
    }

    @Override
    public void onForOfLoop(ForOfLoop node) {
        append("for");
        space().append("(");
        visit(node.getIterator());
        append(" of ");
        visit(node.getIteratedObject());
        append(")").space();
        visit(node.getBody());
    }

    @Override
    public void onWhileLoop(WhileLoop node) {
        append("while").space().append("(");
        visit(node.getCondition());
        append(")");
        handleLoopBody(node.getBody());
    }

    @Override
    public void onArrayComprehensionLoop(ArrayComprehensionLoop node) {
        append("for");
        if(node.isForEach()) {
            append(" each ");
        }
        append("(");
        visit(node.getIterator());
        append(" in ");
        visit(node.getIteratedObject());
        append(")");
    }

    @Override
    public void onNewExpression(NewExpression node) {
        append("new ");
        onFunctionCall(node);
        if(node.getInitializer() != null) {
            visit(node.getInitializer());
        }
    }

    @Override
    public void onRootNode(RootNode root) {
        for(BaseNode statement : root.getElements()) {
            handleBodyStatement(statement);
            addLn();
        }
    }

    @Override
    public void onClassDeclaration(ClassDeclaration node) {
        append("class ");
        visit(node.getName());
        space();
        if (node.getSuperClass() != null) {
            append("extends").space();
            visit(node.getSuperClass());
            space();
        }
        append("{");
        if (node.getElements() != null) {
            indentLn();
            for (BaseNode el: node.getElements()) {
                visit(el);
                addLn();
            }
            unindentLn();
        }
        append("}");
        addLn();
    }

    @Override
    public void onGetAccessor(GetAccessor node) {
        if (node.isStatic()) {
            append("static ");
        }
        append("get ");
        visit(node.getName());
        space().append("()").space();
        visit(node.getBody());
    }

    @Override
    public void onSetAccessor(SetAccessor node) {
        if (node.isStatic()) {
            append("static ");
        }
        append("set ");
        visit(node.getName());
        space().append("(");
        visit(node.getParameter());
        append(")");
        space();
        visit(node.getBody());
    }

    @Override
    public void onDefaultParameter(DefaultParameter node) {
        visit(node.getName());
        space().append("=").space();
        visit(node.getDefaultValue());
    }

    @Override
    public void onSpreadExpression(SpreadExpression node) {
        onUnary(node);
    }

    @Override
    public void onObjectSpread(ObjectSpread node) {
        onUnary(node);
    }

    @Override
    public void onOptionalMemberExpression(OptionalMemberExpression node) {
        visit(node.getLeft());
        if (node.isStartOfOptionalChain()) {
            append(node.getOperator().getValue());
        } else {
            append(".");
        }
        visit(node.getRight());
    }

    @Override
    public void onRestParameter(RestParameter node) {
        onUnary(node);
    }

    @Override
    public void onImportSpecifier(ImportSpecifier node) {
        visit(node.getImportName());
        if (node.getDestination() != null) {
            append(" as ");
            visit(node.getDestination());
        }
    }

    @Override
    public void onImportDeclaration(ImportDeclaration node) {
        append("import");
        if (node.getDefaultBinding() != null) {
            append(" ");
            visit(node.getDefaultBinding());
            if (node.getImports() != null) {
                append(",");
            }
        }
        if (node.getImports() != null) {
            append(" ");
            append("{").space();
            int count = 0;
            for (BaseNode n: node.getImports()) {
                if (count > 0) {
                    append(",").space();
                }
                visit(n);
                count++;
            }
            space().append("}");

        }
        if (node.getNamespace() != null) {
            append(" * as ");
            visit(node.getNamespace());
        }
        append(" from ");
        visit(node.getModule());
        append(";");
    }

    @Override
    public void onExportDeclaration(ExportDeclaration node) {
        append("export ");
        if (node.isDefault()) {
            append("defaut ");
        }
        if (node.isExportAll()) {
            append(" * ");
        }
        visit(node.getDeclaration());
        if (node.getExportSpecifiers() != null) {
            int count = 0;
            for (BaseNode n: node.getExportSpecifiers()) {
                if (count > 0) {
                    append(",").space();
                }
                visit(n);
                count++;
            }
        }
        if (node.getFrom() != null) {
            append("from ");
            visit(node.getFrom());
        }
        append(";");
    }

    @Override
    public void onAwaitExpression(AwaitExpression node) {
        append("await ");
        visit(node.getExpr());
    }

    @Override
    public void onTemplateLiteralExpression(TemplateLiteralExpression node) {
        visit(node.getOperand());
        if (node.getElements() != null) {
            append("`");
            for (BaseNode n: node.getElements()) {
                visit(n);
            }
            append("`");
        }
    }

    @Override
    public void onTemplateLiteralPortion(TemplateLiteralPortion node) {
        visit(node.getValue());
    }

    @Override
    public void onTemplateSubstitution(TemplateSubstitution node) {
        append("${");
        visit(node.getExpr());
        append("}");
    }

    @Override
    public void onComputedName(ComputedName node) {
        append("[");
        visit(node.getExpr());
        append("]");
    }

    @Override
    public void onArrayPattern(ArrayPattern node) {
        append("[");
        printCsv(node.getElements(), true, false);
        append("]");
    }

    @Override
    public void onObjectPattern(ObjectPattern node) {
        append("{");
        printCsv(node.getElements());
        append("}");
    }

    @Override
    public void onFormalParameterList(FormalParameterList node) {
        for (BaseNode param : node.getParams()) {
            visit(param);
        }
    }

    public UnicodeEscapes getUnicodeEscapes() {
        if (_unicodeEscapes == null) {
            _unicodeEscapes = UnicodeEscapes.DefaultEscapes;
        }
        return _unicodeEscapes;
    }

    public void onOptionalMemberLookUpExpression(OptionalMemberLookUpExpression node){
        visit(node.getLeft());
        if (node.isStartOfOptionalChain()) {
            append(node.getOperator().getValue());
        } else {
            append(".");
        }
        visit(node.getRight());
        append("]");
    }

    public void onForAwaitOfStatement(ForAwaitOfStatement node) {
        append("for await");
        space().append("(");
        visit(node.getInitializer());
        space().append("of").space();
        visit(node.getCollection());
        append(")").space();
        handleLoopBody(node.getBody());
    }

    @Override
    public void onPassthroughNode(PassthroughNode node) {
        // Output the preserved modern syntax exactly as it was in the original source
        append(node.getOriginalSyntax());
    }

    @Override
    public void onSimplePassthroughNode(SimplePassthroughNode node) {
        // Output the preserved source exactly as it was in the original source
        append(node.getOriginalSource());
    }

    public void setUnicodeEscapes(UnicodeEscapes unicodeEscapes) {
        _unicodeEscapes = unicodeEscapes;
    }

    public String getNewLine() {
        return _newLine;
    }

    public void setNewLine(String newLine) {
        _newLine = newLine;
    }

    public int getWrapLines() {
        return _wrapLines;
    }

    public void setWrapLines(int wrapLines) {
        _wrapLines = wrapLines;
    }
}
