/*
 * Copyright (c) 2012-2016. Sencha Inc.
 */

package com.sencha.tools.compiler.ast;

import com.google.javascript.jscomp.parsing.parser.trees.ProgramTree;
import com.sencha.tools.compiler.ast.js.*;

/**
 * Example demonstrating the difference between BasicClosureConverter and SimpleClosureConverter.
 * 
 * This example shows how SimpleClosureConverter creates a much simpler AST representation
 * by using SimplePassthroughNode for non-essential constructs while preserving detailed
 * parsing only for the essential nodes needed for property checks (extend, mixins, requires, etc.).
 */
public class SimpleClosureConverterExample {

    public static void main(String[] args) {
        // Example JavaScript code with Ext.define
        String jsCode = 
            "Ext.define('MyApp.view.Main', {\n" +
            "    extend: 'Ext.panel.Panel',\n" +
            "    requires: [\n" +
            "        'MyApp.model.User',\n" +
            "        'MyApp.store.Users'\n" +
            "    ],\n" +
            "    mixins: {\n" +
            "        observable: 'Ext.util.Observable'\n" +
            "    },\n" +
            "    config: {\n" +
            "        title: 'Main View'\n" +
            "    },\n" +
            "    initComponent: function() {\n" +
            "        console.log('Initializing component');\n" +
            "        this.callParent(arguments);\n" +
            "    },\n" +
            "    onRender: function() {\n" +
            "        const items = [...this.items];\n" +
            "        items.forEach(item => item?.show?.());\n" +
            "    }\n" +
            "});";

        System.out.println("=== JavaScript Code ===");
        System.out.println(jsCode);
        System.out.println();

        try {
            // Parse with BasicClosureConverter
            System.out.println("=== BasicClosureConverter Results ===");
            BasicClosureConverter basicConverter = new BasicClosureConverter();
            basicConverter.setOriginalSource(jsCode);
            basicConverter.setSourceName("example.js");
            
            ProgramTree basicProgram = com.sencha.tools.compiler.ast.AstUtil.parseClosureOnly(jsCode, "example.js", null);
            RootNode basicRoot = (RootNode) basicConverter.convert(basicProgram);
            
            analyzeAST(basicRoot, "BasicClosureConverter");
            System.out.println();

            // Parse with SimpleClosureConverter
            System.out.println("=== SimpleClosureConverter Results ===");
            SimpleClosureConverter simpleConverter = new SimpleClosureConverter();
            simpleConverter.setOriginalSource(jsCode);
            simpleConverter.setSourceName("example.js");
            
            ProgramTree simpleProgram = com.sencha.tools.compiler.ast.AstUtil.parseClosureOnly(jsCode, "example.js", null);
            RootNode simpleRoot = (RootNode) simpleConverter.convert(simpleProgram);
            
            analyzeAST(simpleRoot, "SimpleClosureConverter");
            System.out.println();

            // Compare the results
            System.out.println("=== Comparison ===");
            System.out.println("BasicClosureConverter creates detailed AST nodes for all JavaScript constructs.");
            System.out.println("SimpleClosureConverter creates detailed nodes only for essential property checks:");
            System.out.println("- ObjectLiteral (class configuration)");
            System.out.println("- ObjectProperty (extend, mixins, requires, etc.)");
            System.out.println("- StringLiteral (property values)");
            System.out.println("- ArrayLiteral (arrays of requires/mixins)");
            System.out.println("- PropertyGet (property access)");
            System.out.println("- Name (identifiers)");
            System.out.println();
            System.out.println("All other constructs (functions, complex expressions, etc.) are represented");
            System.out.println("as SimplePassthroughNode instances that preserve the original source.");

        } catch (Exception e) {
            System.err.println("Error during parsing: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * Analyzes and prints information about the AST structure.
     */
    private static void analyzeAST(RootNode root, String converterName) {
        System.out.println("Converter: " + converterName);
        System.out.println("Root node type: " + root.getClass().getSimpleName());
        System.out.println("Number of elements: " + root.getElements().size());
        
        // Count different node types
        NodeTypeCounter counter = new NodeTypeCounter();
        root.descend(counter);
        
        System.out.println("Node type distribution:");
        for (String nodeType : counter.getNodeTypes()) {
            int count = counter.getCount(nodeType);
            System.out.println("  " + nodeType + ": " + count);
        }
        
        // Look for SimplePassthroughNode instances
        int passthroughCount = counter.getCount("SimplePassthroughNode");
        if (passthroughCount > 0) {
            System.out.println("SimplePassthroughNode instances found: " + passthroughCount);
            System.out.println("This indicates simplified AST representation.");
        }
        
        // Look for essential nodes
        int objectLiteralCount = counter.getCount("ObjectLiteral");
        int objectPropertyCount = counter.getCount("ObjectProperty");
        int stringLiteralCount = counter.getCount("StringLiteral");
        int arrayLiteralCount = counter.getCount("ArrayLiteral");
        
        System.out.println("Essential nodes for property checks:");
        System.out.println("  ObjectLiteral: " + objectLiteralCount);
        System.out.println("  ObjectProperty: " + objectPropertyCount);
        System.out.println("  StringLiteral: " + stringLiteralCount);
        System.out.println("  ArrayLiteral: " + arrayLiteralCount);
    }

    /**
     * Visitor to count different node types in the AST.
     */
    private static class NodeTypeCounter extends BaseNodeVisitor<Void> {
        private java.util.Map<String, Integer> counts = new java.util.HashMap<>();

        @Override
        public void visit(BaseNode node) {
            if (node != null) {
                String nodeType = node.getClass().getSimpleName();
                counts.put(nodeType, counts.getOrDefault(nodeType, 0) + 1);
                super.visit(node);
            }
        }

        public java.util.Set<String> getNodeTypes() {
            return counts.keySet();
        }

        public int getCount(String nodeType) {
            return counts.getOrDefault(nodeType, 0);
        }
    }
}
