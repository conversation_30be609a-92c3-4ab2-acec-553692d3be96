/*
 * Copyright (c) 2012-2023. Sencha Inc.
 */

package com.sencha.tools.compiler.ast;

import com.google.javascript.jscomp.parsing.parser.SourceFile;
import com.google.javascript.jscomp.parsing.parser.util.ErrorReporter;
import com.google.javascript.jscomp.parsing.parser.util.SourcePosition;
import com.google.javascript.jscomp.parsing.parser.trees.ProgramTree;
import com.sencha.tools.compiler.CompilerMessage;
import com.sencha.tools.compressors.JsLanguageLevel;

/**
 * Utility class for parsing JavaScript with Closure Compiler parser
 * without converting to Sencha AST format.
 */
public class ClosureParserUtil {
    
    /**
     * Parse JavaScript source code using Closure Compiler parser
     * and return the raw ProgramTree without any AST conversion.
     * 
     * @param data The JavaScript source code to parse
     * @param uri The source file URI/name for error reporting
     * @param level The JavaScript language level to use for parsing
     * @return ProgramTree representing the parsed AST
     */
    public static ProgramTree parseOnly(String data, String uri, JsLanguageLevel level) {
        if (level == null) {
            level = JsLanguageLevel.NEXT; // Default to latest
        }

        SourceFile file = new SourceFile(uri, data);

        ErrorReporter reporter = new ErrorReporter() {
            @Override
            protected void reportError(SourcePosition location, String message) {
                CompilerMessage.ClosureError.log(
                    location.source.name, 
                    location.line + 1, 
                    location.column, 
                    message
                );
            }

            @Override
            protected void reportWarning(SourcePosition location, String message) {
                CompilerMessage.ClosureWarn.log(
                    location.source.name, 
                    location.line + 1, 
                    location.column, 
                    message
                );
            }
        };

        com.google.javascript.jscomp.parsing.parser.Parser parser = 
            new com.google.javascript.jscomp.parsing.parser.Parser(
                level.getClosureParserConfig(), 
                reporter, 
                file
            );
        
        return parser.parseProgram();
    }
    
    /**
     * Parse JavaScript source code with default settings
     * 
     * @param data The JavaScript source code to parse
     * @param uri The source file URI/name for error reporting
     * @return ProgramTree representing the parsed AST
     */
    public static ProgramTree parseOnly(String data, String uri) {
        return parseOnly(data, uri, null);
    }
    
    /**
     * Parse JavaScript source code with minimal settings
     * 
     * @param data The JavaScript source code to parse
     * @return ProgramTree representing the parsed AST
     */
    public static ProgramTree parseOnly(String data) {
        return parseOnly(data, "unknown-source");
    }
}