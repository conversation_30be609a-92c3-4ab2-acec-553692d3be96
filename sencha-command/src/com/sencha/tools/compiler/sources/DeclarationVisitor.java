/*
 * Copyright (c) 2012. Sencha Inc.
 */
package com.sencha.tools.compiler.sources;

import com.sencha.logging.SenchaLogManager;
import com.sencha.tools.compiler.CompilerContext;
import com.sencha.tools.compiler.CompilerMessage;
import com.sencha.tools.compiler.ast.AstUtil;
import com.sencha.tools.compiler.ast.js.*;
import com.sencha.tools.compiler.sources.types.ClassDefinition;
import com.sencha.util.Configuration;
import com.sencha.util.PathUtil;
import com.sencha.util.RegexUtil;
import com.sencha.util.StringUtil;
import org.slf4j.Logger;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import static com.sencha.tools.compiler.ast.AstUtil.getObjectProperties;
import static com.sencha.tools.compiler.ast.AstUtil.resolveName;
import static com.sencha.util.StringUtil.formatString;
import static com.sencha.util.StringUtil.isNullOrEmpty;

public class DeclarationVisitor extends BaseDefineVisitor<SourceFileSymbols> {

    private static final Logger _logger = SenchaLogManager.getLogger();

    @Override
    public void onKeywordLiteral(KeywordLiteral keyword) {
        String value = keyword.getValue();
        if(value != null && value.contains("debugger")) {
            CompilerMessage.DebuggerDetected.log(keyword);
        }
        super.onKeywordLiteral(keyword);
    }


    @Override
    public void onObjectLiteral(ObjectLiteral literal) {
        SourceFileSymbols symbols = getContext();
        String fileName = symbols.getSourceFile().getName();
        if(fileName.contains("Ext-more.js")) {
            CompilerContext ctx = getCurrentFile().getScope().getCompilerContext();
            Configuration config = ctx.getConfiguration();
            String frameworkName = (config.has("framework.name"))
                                   ? config.get("framework.name").toString()
                                   : null;

            if("touch".equals(frameworkName)) {
                Map<String, BaseNode> props = getObjectProperties(literal);
                if(props.containsKey("defaultSetupConfig")) {
                    BaseNode defConfig = props.get("defaultSetupConfig");
                    if(defConfig instanceof ObjectLiteral) {
                        _logger.debug("default Touch application config detected");
                        ctx.setDefaultSetupConfig((ObjectLiteral)defConfig);
                    }
                }
            }
        }
        super.onObjectLiteral(literal);
    }

    @Override
    public void onFunctionCall(FunctionCall node) {
        SourceFileSymbols symbols = getContext();
        String fileName = symbols.getSourceFile().getName();
        String name = resolveName(node.getTarget());
        
        if ("Ext.require".equals(name)) {
            processExtRequire(node, symbols);
        } else if ("Ext.syncRequire".equals(name)) {
            processExtRequire(node, symbols);
        } else if ("Ext.Loader.setPath".equals(name)) {
            processSetLoaderPath(node, symbols);
        } else if (
                "Ext.application".equals(name) || 
                ("Ext.setup".equals(name) && !"Ext-more.js".equals(fileName))) {
            
            _logger.debug("adding implicit dependency for Ext.application call");
            List<BaseNode> args = node.getArguments();
            
            symbols.addReference(new Reference(
                "@overrides",
                ReferenceType.OverrideTagRequire,
                symbols.getSourceFile(),
                node
            ));

            if (args.size() == 1 && args.get(0) instanceof ObjectLiteral) {
                symbols.addReference(new Reference(
                        "Ext.app.Application",
                        ReferenceType.ApplicationAutoDependency,
                        symbols.getSourceFile(),
                        args.get(0)));
                
                ObjectLiteral appConfig = (ObjectLiteral) args.get(0);
                processApplicationDependencies(appConfig, symbols);
            } else if(args.size() == 1 && args.get(0) instanceof StringLiteral) {
                symbols.addReference(new Reference(
                        resolveName(args.get(0)),
                        ReferenceType.ApplicationAutoDependency,
                        symbols.getSourceFile(),
                        args.get(0)));
            } else {
                symbols.addReference(new Reference(
                        "Ext.app.Application",
                        ReferenceType.ApplicationAutoDependency,
                        symbols.getSourceFile(),
                        args.get(0)));
            }
        } else if("Ext.define".equals(name) || "Ext.Extend".equals(name)) {
            super.onFunctionCall(node);
            return;
        }
        walk(node);
    }

    // TODO: this code needs to get moved to ReferenceVisitor / implicitClassDependencies
    private void processApplicationDependencies (ObjectLiteral appConfig, SourceFileSymbols symbols) {
        Map<String, BaseNode> configs = getObjectProperties(appConfig);
        
        Configuration config = getCurrentFile().getScope().getCompilerContext().getConfiguration();

        String suffix = config.get("cmd.virtual.app.name.suffix", String.class);
        if(StringUtil.isNullOrEmpty(suffix)) {
            suffix = ".app.$Application";
        }
        
        String appName = resolveName(configs.get("name")),
               tmpClassName = appName + suffix;

        StringLiteral appClassName = new StringLiteral();
        appClassName.setValue(tmpClassName);

        _logger.debug("Creating virtual class definition {}", tmpClassName);
        processDefineObject(appConfig, appClassName, symbols, appName, true, true, "Ext.app.Application");

        // prior to v5 touch needs an autodependency on Ext.event.Dispatcher to
        // address an explicit require in Ext.setup
        if(!config.has("framework.isV5") && config.has("framework.name")) {
            String name = config.get("framework.name").toString();
            if("touch".equals(name)) {
                symbols.addReference(new Reference(
                        "Ext.event.Dispatcher",
                        ReferenceType.ApplicationAutoDependency,
                        symbols.getSourceFile()));
            }
        }

        if(configs.containsKey("autoCreateViewport")){
            BaseNode node = configs.get("autoCreateViewport");
            String value = "false";
            if (node instanceof StringLiteral) {
                value = ((StringLiteral)node).getValue();
            } else if (node instanceof KeywordLiteral) {
                KeywordLiteral lit = (KeywordLiteral) node;
                if(lit.isBooleanLiteral()) {
                    value = lit.getValue();
                }
            }
            
            if("true".equals(value)) {
                String requirement = formatString("%s.view.Viewport", appName);
                _logger.debug("adding implicit requires for {}", requirement);
                symbols.addReference(new Reference(
                        requirement,
                        ReferenceType.ApplicationAutoDependency,
                        symbols.getSourceFile()));
            }
        }
    }

    protected void processSetLoaderPath(FunctionCall node, SourceFileSymbols symbols) {
        List<BaseNode> args = node.getArguments();
        if (args.size() == 1) {
            for (ObjectProperty prop : ((ObjectLiteral) args.get(0)).getElements()) {
                if (prop.getLeft() instanceof StringLiteral) {
                    StringLiteral name = (StringLiteral) prop.getLeft();
                    BaseNode config = prop.getRight();
                    if (config instanceof StringLiteral) {
                        //ctx.setPath(name.getValue(), AstUtil.cast(config, StringLiteral.class).getValue());
                    } else if (config instanceof ArrayLiteral) {
                        //TODO support for jsb4 style multi-root namespace configs
                    }
                } else if (prop.getLeft() instanceof Name) {
                    String name = resolveName(prop.getLeft());
                    BaseNode config = prop.getRight();
                    if (config instanceof StringLiteral) {
                        //ctx.setPath(name, AstUtil.cast(config, StringLiteral.class).getValue());
                    } else if (config instanceof ArrayLiteral) {
                        //TODO support for jsb4 style multi-root namespace configs
                    }
                }
            }
        } else if (args.get(0) instanceof StringLiteral &&
                   args.get(1) instanceof StringLiteral) {
            //StringLiteral name = (StringLiteral) args.get(0);
            //StringLiteral path = (StringLiteral) args.get(1);
            //ctx.setPath(name.getValue(), path.getValue());
        }

    }

    protected void processExtRequire(FunctionCall node, SourceFileSymbols symbols) {
//        BaseNode parent = node.getParent().getParent();
//        if (!(parent instanceof AstRoot)) {
//            CompilerMessage.ExtRequireNoAtRoot.log(node);
//            return;
//        }

        List<BaseNode> args = node.getArguments();
        BaseNode arg = args.get(0);
        boolean skipSimpleRewrite = args.size() > 1;
        
        if (arg instanceof StringLiteral) {
            symbols.addReference(new Reference(
                AstUtil.resolveName(arg),
                ReferenceType.ExtRequire,
                symbols.getSourceFile(),
                arg))
                    .setSkipSimpleRewrite(skipSimpleRewrite)
                    .setReferenceNode(node);
        } else if (arg instanceof ArrayLiteral) {
            for (BaseNode req : ((ArrayLiteral) arg).getElements()) {
                if(req instanceof StringLiteral) {
                    symbols.addReference(new Reference(
                        AstUtil.resolveName(req),
                        ReferenceType.ExtRequire,
                        symbols.getSourceFile(),
                        req))
                        .setSkipSimpleRewrite(skipSimpleRewrite)
                        .setReferenceNode(node);
                }
            }
        }
        
        if(args.size() > 1) {
            
        }
    }

    @Override
    protected boolean processDefineObject (ObjectLiteral obj, StringLiteral className,
                                           SourceFileSymbols symbols) {
        return processDefineObject(obj, className, symbols, null, false, false, null);
    }

    protected boolean processDefineObject (ObjectLiteral obj, StringLiteral className,
                                           SourceFileSymbols symbols, String appName,
                                           boolean virtual, boolean ignore, String baseCls) {
        SourceFile current = getCurrentFile();
        String cname = AstUtil.resolveName(className);
        ClassDefinition def = new ClassDefinition(cname, obj, symbols, baseCls);
        
        if (ignore) {
            def.ignoreNamespace(ClassDefinition.getNamespace(cname));
        }
        
        symbols.addClassDefinition(def);
        if (!isNullOrEmpty(appName)) {
            _logger.debug("detected application name : {}", appName);
            def.setDetectedApplicationName(appName);
        }
        def.setVirtual(virtual);
        return true;
    }

    @Override
    public void onLineComment(LineComment node) {
        SourceFileSymbols symbols = getContext();
        checkFileTags(node, symbols);
        checkFileRequire(node, symbols);
        checkFileDefine(node, symbols);
        checkFileUses(node, symbols);
        checkFileOverride(node, symbols);
        checkTailTag(node, symbols);
    }

    private void checkFileRequire(Comment node, SourceFileSymbols symbols) {
        SourceFile file = getCurrentFile();
        Reference ref;
        for(String path : match(node.getValue(), "require")) {
            boolean fileRef = path.endsWith(".js"),
                    tagRef = path.startsWith("@");
            
            ReferenceType refType = ReferenceType.DirectiveRequire;
            if(fileRef) {
                refType = ReferenceType.DirectiveFileRequire;
            } else if(tagRef) {
                refType = ReferenceType.DirectiveTagRequire;
            }
            
            ref = new Reference(
                    path,
                    refType,
                    file,
                    node);
            
            if (fileRef) {
                ref.setIsFileReference(true);
            }
            symbols.addReference(ref);

            if (fileRef) {
                // determine if the referenced file has not been loaded
                // if not, load and scan now
                SourceFile sf = symbols.getSourceFile();
                String dir = sf.getDirectory();
                String filePath = path;
                if (!PathUtil.isAbsolute(filePath)) {
                    filePath = PathUtil.getAbsolutePath(dir, path);
                }
                // trigger the load of the indirect file
                SourceFile target = sf.getScope().getSourceFile(filePath);
                if (target != null && target != sf) {
                    // trigger the new file to create it's symbols, and also
                    // triggering other loads of directly referenced files
                    SourceFileSymbols targetSymbols = target.getSymbols();
                }
            }
        }
    }

    private void checkFileDefine(Comment node, SourceFileSymbols symbols) {
        for(String define : match(node.getValue(), "define")) {
            symbols.addExplicitDefine(define);
        }
    }

    private void checkFileUses(Comment node, SourceFileSymbols symbols) {
        SourceFile file = getCurrentFile();
        Reference ref;
        for(String path : match(node.getValue(), "uses")) {
            boolean fileRef = path.endsWith(".js"),
                    tagRef = path.startsWith("@");
            
            ReferenceType refType = ReferenceType.DirectiveUses;
            if(fileRef) {
                refType = ReferenceType.DirectiveFileUses;
            } else if(tagRef) {
                refType = ReferenceType.DirectiveTagUses;
            }
            
            ref = new Reference(
                    path,
                    refType,
                    file,
                    node);
            
            if (path.endsWith(".js")) {
                ref.setIsFileReference(true);
            }
            symbols.addReference(ref);
        }
    }

    private void checkFileOverride(Comment node, SourceFileSymbols symbols) {
        for(String tag : match(node.getValue(), "override")) {
            symbols.setOverrideTarget(tag);
        }
    }
    
    private void checkFileTags(Comment node, SourceFileSymbols symbols) {
        for(String tag : match(node.getValue(), "tag")) {
            symbols.addTag(tag);
        }
    }

    private void checkTailTag(Comment node, SourceFileSymbols symbols) {
        for(String tag : match(node.getValue(), "tail", null)) {
            symbols.addTag(tag);
        }
        final String tag = "@tail";
        String value = node.getValue();
        int position = value.indexOf(tag);
        if (position >= 0) {
            symbols.setTailFile(true);
        }
    }

    public static List<String> match(String comment, String tag) {
        return match(comment, tag, ",");
    }
    
    public static List<String> match(String comment, String tag, String delim) {
        Pattern regex = RegexUtil.getInstance().get(StringUtil.isNullOrEmpty(delim)
                                ? "^//\\s*?[@|#]" + tag + "\\s*?"
                                : "^//\\s*?[@|#]" + tag + "\\s+(.*?)");

        return match(comment, regex, delim);
    }
    
    public static List<String> match(String comment, Pattern pat) {
        return match(comment, pat, ",");
    }
    
    public static List<String> match(String comment, Pattern pat, String delim) {
        String input = comment.trim();
        List<String> extracted = new ArrayList<String>();
        Matcher m = pat.matcher(input);
        boolean matched = m.matches();
        if(matched) {
            if(!StringUtil.isNullOrEmpty(delim) && m.groupCount() == 1) {
                String group1 = m.group(1);
                String[] elements = group1.split(delim);
                for(String el : elements) {
                    extracted.add(el.trim());
                }
            } else {
                extracted.add(m.group(0).trim());
            }
        }
        return extracted;
    }

    
    @Override
    public void visit(BaseNode node) {
        if(node != null) {
            SourceFile sf = getCurrentFile();
            node.setSourceFile(sf);
            super.visit(node);
        }
    }
}
