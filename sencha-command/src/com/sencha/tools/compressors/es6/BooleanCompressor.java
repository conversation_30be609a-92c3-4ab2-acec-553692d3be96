/*
 * Copyright (c) 2012-2016. Sencha Inc.
 */

package com.sencha.tools.compressors.es6;

import com.sencha.tools.compiler.ast.js.*;

public class BooleanCompressor implements CompressorTransform {
    
    private static class BooleanVisitor extends OptimizedNodeVisitor {
        private static Unary True;
        private static Unary False;

        static {
            NumberLiteral one = new NumberLiteral();
            one.setValue(1);
            NumberLiteral zero = new NumberLiteral();
            zero.setValue(0);
            True = new Unary();
            True.setOperator(Operators.getByValue("!"));
            True.setOperand(zero);
            False = new Unary();
            False.setOperator(Operators.getByValue("!"));
            False.setOperand(one);
        }

        @Override
        public void onKeywordLiteral(KeywordLiteral node) {
            String value = node.getValue();
            if (value != null) {
                if (value.equals("true")) {
                    node.setOptimized(True);
                }
                else if (value.equals("false")) {
                    node.setOptimized(False);
                }
            }
        }


    }
    
    @Override
    public void compress(BaseNode ast) {
        BooleanVisitor vis = new BooleanVisitor();
        vis.visit(ast);
    }
}
